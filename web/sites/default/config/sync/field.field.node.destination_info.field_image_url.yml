uuid: ced0ff33-5282-4b9a-a027-b6b7d7d6bf60
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_image_url
    - node.type.destination_info
id: node.destination_info.field_image_url
field_name: field_image_url
entity_type: node
bundle: destination_info
label: 'Image URL'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string_long
