uuid: 72604f18-065a-4a28-aba9-75efb316bf64
langcode: en
status: true
dependencies:
  config:
    - block_content.type.site_switch
    - entityqueue.entity_queue.luxair_sites_switch
  module:
    - block_content
    - entityqueue
    - user
id: luxairgroup_sites
label: 'LuxairGroup Sites'
module: views
description: ''
tag: ''
base_table: block_content_field_data
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'LuxairGroup Sites'
      fields:
        info:
          id: info
          table: block_content_field_data
          field: info
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: info
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 6
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        entityqueue_relationship:
          id: entityqueue_relationship
          table: block_content_field_data
          field: entityqueue_relationship
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: block_content
          plugin_id: entity_queue_position
          order: ASC
          expose:
            label: ''
            field_identifier: entityqueue_relationship
          exposed: false
      arguments: {  }
      filters:
        type:
          id: type
          table: block_content_field_data
          field: type
          entity_type: block_content
          entity_field: type
          plugin_id: bundle
          value:
            site_switch: site_switch
          expose:
            operator_limit_selection: false
            operator_list: {  }
        default_langcode:
          id: default_langcode
          table: block_content_field_data
          field: default_langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: block_content
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          row_class: ''
          default_row_class: false
          uses_fields: true
      row:
        type: 'entity:block_content'
        options:
          relationship: none
          view_mode: default
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        entityqueue_relationship:
          id: entityqueue_relationship
          table: block_content_field_data
          field: entityqueue_relationship
          relationship: none
          group_type: group
          admin_label: 'Custom block queue'
          entity_type: block_content
          plugin_id: entity_queue
          required: false
          limit_queue: luxair_sites_switch
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags:
        - 'config:entityqueue.entity_queue.luxair_sites_switch'
        - entity_field_info
        - views_data
  block_sites:
    id: block_sites
    display_title: Block
    display_plugin: block
    position: 1
    display_options:
      rendering_language: '***LANGUAGE_language_interface***'
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - user.permissions
      tags:
        - 'config:entityqueue.entity_queue.luxair_sites_switch'
        - entity_field_info
        - views_data
