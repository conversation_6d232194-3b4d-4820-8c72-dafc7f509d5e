label: 'Contenu(s) planifié(s)'
display:
  default:
    display_title: Master
    display_options:
      exposed_form:
        options:
          submit_button: Filter
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
      pager:
        options:
          tags:
            previous: "‹\_précédent"
            next: "suivant\_›"
            first: "«\_premier"
            last: "dernier\_»"
      fields:
        title:
          label: Title
        type:
          label: 'Type de contenu'
        name:
          label: Auteur
        status:
          label: Status
          settings:
            format_custom_true: Publié
            format_custom_false: 'Non publié'
        publish_on:
          label: 'Publier le'
        unpublish_on:
          label: 'Dépublier le'
        operations:
          label: Operations
      filters:
        title:
          expose:
            label: Title
        type:
          expose:
            label: 'Content type'
        status:
          expose:
            label: Status
          group_info:
            label: 'Published status'
            group_items:
              1:
                title: Publié
              2:
                title: 'Non publié'
        langcode:
          expose:
            label: Language
      title: 'Contenu(s) planifié(s)'
      empty:
        area_text_custom:
          content: 'Aucun contenu planifié.'
  overview:
    display_title: "Vue d'ensemble des contenus"
    display_options:
      menu:
        title: 'Contenu(s) planifié(s)'
      tab_options:
        title: Content
        description: 'Chercher et gérer le contenu planifié'
  user_page:
    display_options:
      menu:
        title: Planifié
      tab_options:
        title: Content
        description: 'Chercher et gérer le contenu planifié'
      arguments:
        uid:
          exception:
            title: All
      display_comment: 'Access to the user view is controlled via a custom RouteSubscriber. The high-level "view published content" permission is added to satisfy the security_review module'
