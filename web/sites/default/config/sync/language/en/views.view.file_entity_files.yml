description: 'Find and manage files.'
display:
  default:
    display_title: Master
    display_options:
      exposed_form:
        options:
          submit_button: Filter
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
      pager:
        options:
          tags:
            previous: "‹\_précédent"
            next: "suivant\_›"
          expose:
            items_per_page_label: 'Items per page'
            items_per_page_options_all_label: '- All -'
            offset_label: Offset
      fields:
        bulk_form:
          action_title: 'Pour la sélection'
        fid:
          label: Fid
        filename:
          label: Nom
        filemime:
          label: 'Mime type'
        filesize:
          label: Size
        status:
          label: Status
        changed:
          label: Changed
        created:
          label: Créé
        count:
          label: 'Used in'
          format_plural_string: !!binary MSBwbGFjZQNAY291bnQgcGxhY2Vz
        operations:
          label: Operations
      filters:
        filename:
          expose:
            label: Filename
        filemime:
          expose:
            label: 'Type MIME'
        status:
          expose:
            label: Status
      title: Files
      empty:
        area_text_custom:
          content: 'No files available.'
  overview:
    display_title: 'Files overview'
    display_options:
      menu:
        title: Files
  usage:
    display_title: 'File usage'
    display_options:
      empty:
        area_text_custom:
          content: "Ce fichier n'est pas utilisé actuellement."
      pager:
        options:
          tags:
            previous: "‹\_précédent"
            next: "suivant\_›"
          expose:
            items_per_page_label: 'Items per page'
            items_per_page_options_all_label: '- All -'
            offset_label: Offset
      fields:
        entity_label:
          label: Entity
        type:
          label: 'Entity type'
        module:
          label: 'Registering module'
        count:
          label: 'Use count'
          format_plural_string: !!binary MSBwbGFjZQNAY291bnQgcGxhY2Vz
      title: 'File usage'
      arguments:
        fid:
          exception:
            title: All
          title: 'File usage information for {{ arguments.fid }}'
      menu:
        title: Utilisation
