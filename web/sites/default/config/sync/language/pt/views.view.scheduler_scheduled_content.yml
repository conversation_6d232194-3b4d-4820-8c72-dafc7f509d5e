label: 'Contenu(s) planifié(s)'
display:
  default:
    display_options:
      exposed_form:
        options:
          submit_button: Filter
      pager:
        options:
          tags:
            previous: "‹\_précédent"
            next: "suivant\_›"
            first: "«\_premier"
            last: "dernier\_»"
      fields:
        type:
          label: 'Type de contenu'
        name:
          label: Auteur
        status:
          label: Status
          settings:
            format_custom_true: Publié
            format_custom_false: 'Non publié'
        publish_on:
          label: 'Publier le'
        unpublish_on:
          label: 'Dépublier le'
        operations:
          label: Operations
      filters:
        type:
          expose:
            label: 'Content type'
        status:
          expose:
            label: Status
          group_info:
            group_items:
              1:
                title: Publié
              2:
                title: 'Non publié'
      title: 'Contenu(s) planifié(s)'
      empty:
        area_text_custom:
          content: 'Aucun contenu planifié.'
  overview:
    display_title: "Vue d'ensemble des contenus"
    display_options:
      menu:
        title: 'Contenu(s) planifié(s)'
      tab_options:
        description: 'Chercher et gérer le contenu planifié'
  user_page:
    display_options:
      menu:
        title: Planifié
      tab_options:
        description: 'Cher<PERSON> et gérer le contenu planifié'
      display_comment: 'Access to the user view is controlled via a custom RouteSubscriber. The high-level "view published content" permission is added to satisfy the security_review module'
