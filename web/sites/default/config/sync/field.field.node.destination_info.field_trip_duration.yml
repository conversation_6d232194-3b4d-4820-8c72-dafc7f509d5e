uuid: 6aed1ece-2aa1-4581-8670-e85065ffcfcf
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_trip_duration
    - node.type.destination_info
id: node.destination_info.field_trip_duration
field_name: field_trip_duration
entity_type: node
bundle: destination_info
label: 'Trip duration'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: 1
  max: 99
  prefix: ''
  suffix: ''
field_type: integer
