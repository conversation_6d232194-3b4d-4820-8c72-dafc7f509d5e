uuid: d6b76289-e7c0-470e-96a3-74d75d3256ee
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_is_new
    - node.type.destination_info
id: node.destination_info.field_is_new
field_name: field_is_new
entity_type: node
bundle: destination_info
label: 'Is new destination?'
description: 'This will display the "New" icon'
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: New
  off_label: 'Not New'
field_type: boolean
