uuid: e034c3f5-3dd2-44ed-9563-f2680c57e3d0
langcode: fr
status: true
dependencies:
  config:
    - system.menu.admin
  module:
    - node
    - user
  enforced:
    module:
      - scheduler
_core:
  default_config_hash: I27B122Cg9EPsoDAkmbLyOzenA1f7goNOeONt7yBwFs
id: scheduler_scheduled_content
label: 'Contenu(s) planifié(s)'
module: views
description: 'Find and manage scheduled content.'
tag: ''
base_table: node_field_revision
base_field: vid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'Contenu(s) planifié(s)'
      fields:
        node_bulk_form:
          id: node_bulk_form
          table: node
          field: node_bulk_form
          relationship: nid
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: node_bulk_form
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          action_title: Action
          include_exclude: exclude
          selected_actions: {  }
        title:
          id: title
          table: node_field_revision
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: Title
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        type:
          id: type
          table: node_field_data
          field: type
          relationship: nid
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: field
          label: 'Type de contenu'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        name:
          id: name
          table: users_field_data
          field: name
          relationship: revision_uid
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: name
          plugin_id: field
          label: Auteur
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: user_name
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        status:
          id: status
          table: node_field_revision
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: status
          plugin_id: field
          label: Status
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: custom
            format_custom_false: 'Non publié'
            format_custom_true: Publié
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        publish_on:
          id: publish_on
          table: node_field_revision
          field: publish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: publish_on
          plugin_id: field
          label: 'Publier le'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: short
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        unpublish_on:
          id: unpublish_on
          table: node_field_revision
          field: unpublish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: unpublish_on
          plugin_id: field
          label: 'Dépublier le'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: short
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        operations:
          id: operations
          table: node_revision
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: entity_operations
          label: Operations
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 50
          total_pages: null
          id: 0
          tags:
            next: "suivant\_›"
            previous: "‹\_précédent"
            first: "«\_premier"
            last: "dernier\_»"
      exposed_form:
        type: basic
        options:
          submit_button: Filter
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'view scheduled content'
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'Aucun contenu planifié.'
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        title:
          id: title
          table: node_field_revision
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_op
            label: Title
            description: ''
            use_operator: false
            operator: title_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: title
            required: false
            remember: false
            multiple: false
            remember_roles:
              anonymous: '0'
              authenticated: '0'
              administrator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: nid
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: type_op
            label: 'Content type'
            description: ''
            use_operator: false
            operator: type_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: type
            required: false
            remember: false
            multiple: false
            remember_roles:
              anonymous: '0'
              authenticated: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: node_field_revision
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: Status
            description: ''
            use_operator: false
            operator: status_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: status
            required: false
            remember: false
            multiple: false
            remember_roles:
              anonymous: '0'
              authenticated: '0'
              administrator: '0'
          is_grouped: true
          group_info:
            label: 'Published status'
            description: ''
            identifier: status
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: Publié
                operator: '='
                value: '1'
              2:
                title: 'Non publié'
                operator: '='
                value: '0'
        langcode:
          id: langcode
          table: node_field_revision
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: language
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: langcode_op
            label: Language
            description: ''
            use_operator: false
            operator: langcode_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: langcode
            required: false
            remember: false
            multiple: false
            remember_roles:
              anonymous: '0'
              authenticated: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        publish_on:
          id: publish_on
          table: node_field_revision
          field: publish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: publish_on
          plugin_id: date
          operator: 'not empty'
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 2
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              anonymous: '0'
              authenticated: '0'
              administrator: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        unpublish_on:
          id: unpublish_on
          table: node_field_revision
          field: unpublish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: unpublish_on
          plugin_id: date
          operator: 'not empty'
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 2
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              anonymous: '0'
              authenticated: '0'
              administrator: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        latest_translation_affected_revision:
          id: latest_translation_affected_revision
          table: node_revision
          field: latest_translation_affected_revision
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: latest_translation_affected_revision
          operator: '='
          value: ''
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            node_bulk_form: node_bulk_form
            title: title
            type: type
            name: name
            status: status
            publish_on: publish_on
            unpublish_on: unpublish_on
            operations: operations
          default: '-1'
          info:
            node_bulk_form:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            title:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            type:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            name:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            status:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            publish_on:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            unpublish_on:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: true
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        nid:
          id: nid
          table: node_field_revision
          field: nid
          relationship: none
          group_type: group
          admin_label: 'node id'
          entity_type: node
          entity_field: nid
          plugin_id: standard
          required: false
        revision_uid:
          id: revision_uid
          table: node_revision
          field: revision_uid
          relationship: none
          group_type: group
          admin_label: 'revision user id'
          entity_type: node
          entity_field: revision_uid
          plugin_id: standard
          required: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  overview:
    id: overview
    display_title: "Vue d'ensemble des contenus"
    display_plugin: page
    position: 1
    display_options:
      display_description: "Overview of all scheduled content, as a tab on main 'content admin' page"
      display_comment: "Revision nid relationship is required because the content type is only stored at 'content' level, not 'content revision' level."
      display_extenders: {  }
      path: admin/content/scheduled
      menu:
        type: normal
        title: 'Contenu(s) planifié(s)'
        description: 'Content that is scheduled for publishing or unpublishing'
        weight: -10
        expanded: false
        menu_name: admin
        parent: system.admin_content
        context: '0'
      tab_options:
        type: normal
        title: Content
        description: 'Chercher et gérer le contenu planifié'
        weight: -10
        menu_name: admin
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  user_page:
    id: user_page
    display_title: User
    display_plugin: page
    position: 2
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No scheduled content for user {{ arguments.uid }}'
          tokenize: true
      arguments:
        uid:
          id: uid
          table: node_field_revision
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: uid
          plugin_id: entity_target_id
          default_action: empty
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:user'
            fail: 'not found'
          validate_options:
            access: false
            operation: view
            multiple: 0
            restrict_roles: false
            roles: {  }
          break_phrase: false
          not: false
          target_entity_type_id: user
      defaults:
        empty: false
        access: false
        arguments: false
        filters: true
        filter_groups: true
      display_description: "Scheduled content tab on user profile, showing just that user's scheduled content"
      display_comment: 'Access to the user view is controlled via a custom RouteSubscriber. The high-level "view published content" permission is added to satisfy the security_review module'
      display_extenders: {  }
      path: user/%user/scheduled
      menu:
        type: tab
        title: Planifié
        description: ''
        weight: -10
        menu_name: admin
        parent: user.page
        context: '0'
      tab_options:
        type: normal
        title: Content
        description: 'Chercher et gérer le contenu planifié'
        weight: -10
        menu_name: admin
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
