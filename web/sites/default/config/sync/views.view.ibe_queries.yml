uuid: e059ebea-aad7-48eb-84f2-6a9467936876
langcode: fr
status: true
dependencies:
  module:
    - ibe_query_builder
    - luxair_teasers
    - node
    - paragraphs
    - user
id: ibe_queries
label: 'IBE Queries'
module: views
description: ''
tag: ''
base_table: ibe_query
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'IBE Queries'
      fields:
        id:
          id: id
          table: ibe_query
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: id
          plugin_id: field
          label: ID
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        edit_ibe_query:
          id: edit_ibe_query
          table: ibe_query
          field: edit_ibe_query
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          plugin_id: entity_link_edit
          label: 'Link to edit IBE Query'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: edit
        name:
          id: name
          table: ibe_query
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: name
          plugin_id: field
          label: Name
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: true
            path: 'admin/content/ibe_query/{{ id }}/edit'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        query__value:
          id: query__value
          table: ibe_query
          field: query__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: query
          plugin_id: field
          label: Query
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: query_field_formatter
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        time_to_expire:
          id: time_to_expire
          table: ibe_query
          field: time_to_expire
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          plugin_id: ibe_time_to_expire
          label: Expires
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        operations:
          id: operations
          table: ibe_query
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: entity_operations
          label: 'Operations links'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 50
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access ibe query overview'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments: {  }
      filters:
        name:
          id: name
          table: ibe_query
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: name
          plugin_id: string
          operator: word
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: name_op
            label: 'Query Name'
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              content_manager: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        query__value:
          id: query__value
          table: ibe_query
          field: query__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: query
          plugin_id: views_autocomplete_filters_string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: query__value_op
            label: 'Query Value'
            description: ''
            use_operator: false
            operator: query__value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: query__value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              content_manager: '0'
              corporate: '0'
              3dsecuremanager: '0'
              darksite_manager: '0'
            placeholder: ''
            autocomplete_filter: 0
            autocomplete_min_chars: '0'
            autocomplete_items: '10'
            autocomplete_field: query__value
            autocomplete_raw_suggestion: 1
            autocomplete_raw_dropdown: 1
            autocomplete_dependent: 0
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        query__date_from:
          id: query__date_from
          table: ibe_query
          field: query__date_from
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: query
          plugin_id: date
          operator: '>='
          value:
            min: ''
            max: ''
            value: ''
            type: date
            widget: date
          group: 1
          exposed: true
          expose:
            operator_id: query__date_from_op
            label: 'Date from'
            description: ''
            use_operator: false
            operator: query__date_from_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: query__date_from
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              content_manager: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        query__date_to:
          id: query__date_to
          table: ibe_query
          field: query__date_to
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: query
          plugin_id: date
          operator: '<='
          value:
            min: ''
            max: ''
            value: ''
            type: date
            widget: date
          group: 1
          exposed: true
          expose:
            operator_id: query__date_to_op
            label: 'Date to'
            description: ''
            use_operator: false
            operator: query__date_to_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: query__date_to
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              content_manager: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            id: id
            edit_ibe_query: edit_ibe_query
            name: name
            query__value: query__value
            time_to_expire: time_to_expire
            operations: operations
          default: time_to_expire
          info:
            id:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            edit_ibe_query:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            name:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            query__value:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            time_to_expire:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  attachment_1:
    id: attachment_1
    display_title: 'Teasers using query'
    display_plugin: attachment
    position: 3
    display_options:
      title: 'Pages using this query:'
      fields:
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_content
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        langcode_1:
          id: langcode_1
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_content_1
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        langcode_2:
          id: langcode_2
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_content_2
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        langcode_3:
          id: langcode_3
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_content_3
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        langcode_4:
          id: langcode_4
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_stage
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        langcode_5:
          id: langcode_5
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_stage_1
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        langcode_6:
          id: langcode_6
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_stage_2
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        langcode_7:
          id: langcode_7
          table: node_field_data
          field: langcode
          relationship: reverse__node__field_paragraph_stage_3
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: language
          settings:
            link_to_entity: false
            native_language: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_content
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title }} ({{ langcode }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title_1:
          id: title_1
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_content_1
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title_1 }} ({{ langcode_1 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title_2:
          id: title_2
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_content_2
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title_2 }} ({{ langcode_2 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title_3:
          id: title_3
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_content_3
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title_3 }} ({{ langcode_3 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title_4:
          id: title_4
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_stage
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title_4 }} ({{ langcode_4 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title_5:
          id: title_5
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_stage_1
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title_5 }} ({{ langcode_5 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title_6:
          id: title_6
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_stage_2
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title_6 }} ({{ langcode_6 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title_7:
          id: title_7
          table: node_field_data
          field: title
          relationship: reverse__node__field_paragraph_stage_3
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ title_7 }} ({{ langcode_7 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: none
        options:
          offset: 0
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'This query is not used in any teaser.'
            format: default
          tokenize: false
      arguments:
        id:
          id: id
          table: ibe_query
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: id
          plugin_id: numeric
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: raw
          default_argument_options:
            index: 4
            use_alias: false
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters: {  }
      filter_groups:
        operator: AND
        groups: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options: {  }
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: true
          replica: false
          query_tags: {  }
      defaults:
        empty: false
        query: false
        title: false
        style: false
        row: false
        relationships: false
        fields: false
        arguments: false
        filters: false
        filter_groups: false
        header: false
      relationships:
        reverse__teaser__field_query_1:
          id: reverse__teaser__field_query_1
          table: ibe_query
          field: reverse__teaser__field_query_1
          relationship: none
          group_type: group
          admin_label: 'teaser using field_query_1'
          entity_type: ibe_query
          plugin_id: entity_reverse
          required: false
        reverse__teaser__field_query_2:
          id: reverse__teaser__field_query_2
          table: ibe_query
          field: reverse__teaser__field_query_2
          relationship: none
          group_type: group
          admin_label: 'teaser using field_query_2'
          entity_type: ibe_query
          plugin_id: entity_reverse
          required: false
        reverse__teaser__field_query_3:
          id: reverse__teaser__field_query_3
          table: ibe_query
          field: reverse__teaser__field_query_3
          relationship: none
          group_type: group
          admin_label: 'Teaser using field_query_3'
          entity_type: ibe_query
          plugin_id: entity_reverse
          required: false
        reverse__teaser__field_query_4:
          id: reverse__teaser__field_query_4
          table: ibe_query
          field: reverse__teaser__field_query_4
          relationship: none
          group_type: group
          admin_label: 'Teaser using field_query_4'
          entity_type: ibe_query
          plugin_id: entity_reverse
          required: false
        reverse__paragraph__field_teaser:
          id: reverse__paragraph__field_teaser
          table: teaser_field_data
          field: reverse__paragraph__field_teaser
          relationship: reverse__teaser__field_query_1
          group_type: group
          admin_label: 'Paragraph using field_query_1'
          entity_type: teaser
          plugin_id: entity_reverse
          required: false
        reverse__paragraph__field_teaser_1:
          id: reverse__paragraph__field_teaser_1
          table: teaser_field_data
          field: reverse__paragraph__field_teaser
          relationship: reverse__teaser__field_query_2
          group_type: group
          admin_label: 'Paragraph using field_query_2'
          entity_type: teaser
          plugin_id: entity_reverse
          required: false
        reverse__paragraph__field_teaser_2:
          id: reverse__paragraph__field_teaser_2
          table: teaser_field_data
          field: reverse__paragraph__field_teaser
          relationship: reverse__teaser__field_query_3
          group_type: group
          admin_label: 'Paragraph using field_query_3'
          entity_type: teaser
          plugin_id: entity_reverse
          required: false
        reverse__paragraph__field_teaser_3:
          id: reverse__paragraph__field_teaser_3
          table: teaser_field_data
          field: reverse__paragraph__field_teaser
          relationship: reverse__teaser__field_query_4
          group_type: group
          admin_label: 'Paragraph using field_query_4'
          entity_type: teaser
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_content:
          id: reverse__node__field_paragraph_content
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_content
          relationship: reverse__paragraph__field_teaser
          group_type: group
          admin_label: 'content using query_1'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_content_1:
          id: reverse__node__field_paragraph_content_1
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_content
          relationship: reverse__paragraph__field_teaser_1
          group_type: group
          admin_label: 'Content using query_2'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_content_2:
          id: reverse__node__field_paragraph_content_2
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_content
          relationship: reverse__paragraph__field_teaser_2
          group_type: group
          admin_label: 'Content using query_3'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_content_3:
          id: reverse__node__field_paragraph_content_3
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_content
          relationship: reverse__paragraph__field_teaser_3
          group_type: group
          admin_label: 'Content using query_4'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_stage:
          id: reverse__node__field_paragraph_stage
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_stage
          relationship: reverse__paragraph__field_teaser
          group_type: group
          admin_label: 'Content using field_paragraph_stage query_1'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_stage_1:
          id: reverse__node__field_paragraph_stage_1
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_stage
          relationship: reverse__paragraph__field_teaser_1
          group_type: group
          admin_label: 'Content using field_paragraph_stage query_2'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_stage_2:
          id: reverse__node__field_paragraph_stage_2
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_stage
          relationship: reverse__paragraph__field_teaser_2
          group_type: group
          admin_label: 'Content using field_paragraph_stage query_3'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
        reverse__node__field_paragraph_stage_3:
          id: reverse__node__field_paragraph_stage_3
          table: paragraphs_item_field_data
          field: reverse__node__field_paragraph_stage
          relationship: reverse__paragraph__field_teaser_3
          group_type: group
          admin_label: 'Content using field_paragraph_stage query_4'
          entity_type: paragraph
          plugin_id: entity_reverse
          required: false
      display_description: ''
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<h3>Pages using this query:</h3>'
            format: default
          tokenize: false
      display_extenders: {  }
      displays:
        query_details: query_details
      attachment_position: after
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - user.permissions
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      fields:
        id:
          id: id
          table: ibe_query
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: id
          plugin_id: field
          label: ID
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        edit_ibe_query:
          id: edit_ibe_query
          table: ibe_query
          field: edit_ibe_query
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          plugin_id: entity_link_edit
          label: 'Link to edit IBE Query'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: edit
        name:
          id: name
          table: ibe_query
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: name
          plugin_id: field
          label: Name
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: true
            path: 'admin/content/ibe_query/{{ id }}/edit'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        query__value:
          id: query__value
          table: ibe_query
          field: query__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: query
          plugin_id: field
          label: Query
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: query_field_formatter
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        time_to_expire:
          id: time_to_expire
          table: ibe_query
          field: time_to_expire
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          plugin_id: ibe_time_to_expire
          label: Expires
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        operations:
          id: operations
          table: ibe_query
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: entity_operations
          label: 'Operations links'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
        id_1:
          id: id_1
          table: ibe_query
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: id
          plugin_id: field
          label: 'Query usage'
          exclude: false
          alter:
            alter_text: true
            text: 'See query usage'
            make_link: true
            path: '/admin/content/ibe-queries/query-usage/{{ id }}'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      defaults:
        fields: false
      display_extenders: {  }
      path: admin/content/ibe-queries
      menu:
        type: tab
        title: 'IBE Queries'
        description: ''
        weight: 0
        expanded: false
        menu_name: admin
        parent: ''
        context: '0'
      'route name': entity.ibe_query.collection
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  query_details:
    id: query_details
    display_title: 'Query details'
    display_plugin: page
    position: 2
    display_options:
      title: 'Query details'
      fields:
        id:
          id: id
          table: ibe_query
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: id
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        view_ibe_query:
          id: view_ibe_query
          table: ibe_query
          field: view_ibe_query
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          plugin_id: entity_link
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: view
          output_url_as_text: false
          absolute: false
        name:
          id: name
          table: ibe_query
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: name
          plugin_id: field
          label: 'Query name'
          exclude: false
          alter:
            alter_text: false
            text: '{{ name }} {{ dropbutton }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        edit_ibe_query:
          id: edit_ibe_query
          table: ibe_query
          field: edit_ibe_query
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          plugin_id: entity_link_edit
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: 'Edit query {{ name }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: 'Edit query'
        dropbutton:
          id: dropbutton
          table: views
          field: dropbutton
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: dropbutton
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: false
          fields:
            edit_ibe_query: edit_ibe_query
            id: '0'
            view_ibe_query: '0'
            name: '0'
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 1
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'No query ID specified or query not found.'
            format: default
          tokenize: false
      arguments:
        id:
          id: id
          table: ibe_query
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: ibe_query
          entity_field: id
          plugin_id: numeric
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: true
          title: 'Details for query {{ arguments.id }}'
          default_argument_type: raw
          default_argument_options:
            index: 4
            use_alias: false
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters: {  }
      filter_groups:
        operator: AND
        groups: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options: {  }
      defaults:
        empty: false
        title: false
        pager: false
        style: false
        row: false
        fields: false
        arguments: false
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
      path: admin/content/ibe-queries/query-usage/%
      menu:
        type: none
        title: 'IBE Queries usage'
        description: ''
        weight: 0
        expanded: false
        menu_name: admin
        parent: ''
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - user.permissions
      tags: {  }
