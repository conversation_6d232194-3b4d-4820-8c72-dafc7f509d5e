uuid: 06d38a26-106d-434b-a345-9aa1b194f94d
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_promos
    - node.type.destination_info
    - taxonomy.vocabulary.promos
id: node.destination_info.field_promos
field_name: field_promos
entity_type: node
bundle: destination_info
label: Promos
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      promos: promos
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
