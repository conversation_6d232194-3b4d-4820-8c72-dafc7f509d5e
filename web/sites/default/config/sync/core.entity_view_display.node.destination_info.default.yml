uuid: 6f459b29-1e01-4454-97d5-66b4227baee6
langcode: en
status: true
dependencies:
  config:
    - field.field.node.destination_info.field_arrguides
    - field.field.node.destination_info.field_bewotec
    - field.field.node.destination_info.field_davinci
    - field.field.node.destination_info.field_destination_country
    - field.field.node.destination_info.field_flight_duration
    - field.field.node.destination_info.field_iata
    - field.field.node.destination_info.field_image_alt
    - field.field.node.destination_info.field_image_url
    - field.field.node.destination_info.field_is_new
    - field.field.node.destination_info.field_lg_destination_order
    - field.field.node.destination_info.field_lg_destination_url
    - field.field.node.destination_info.field_lgit_destination_order
    - field.field.node.destination_info.field_lgit_destination_url
    - field.field.node.destination_info.field_promos
    - field.field.node.destination_info.field_themes
    - field.field.node.destination_info.field_trip_duration
    - node.type.destination_info
  module:
    - user
id: node.destination_info.default
targetEntityType: node
bundle: destination_info
mode: default
content:
  field_arrguides:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 106
    region: content
  field_bewotec:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 105
    region: content
  field_davinci:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 107
    region: content
  field_destination_country:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 102
    region: content
  field_flight_duration:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 114
    region: content
  field_iata:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 104
    region: content
  field_image_alt:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 109
    region: content
  field_image_url:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 108
    region: content
  field_is_new:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 112
    region: content
  field_lg_destination_order:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 115
    region: content
  field_lg_destination_url:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 110
    region: content
  field_lgit_destination_order:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 116
    region: content
  field_lgit_destination_url:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 111
    region: content
  field_promos:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 103
    region: content
  field_themes:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 101
    region: content
  field_trip_duration:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 113
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
