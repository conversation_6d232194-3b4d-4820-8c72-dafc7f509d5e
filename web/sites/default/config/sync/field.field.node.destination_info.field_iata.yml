uuid: bb70f04e-5d23-42cf-bae9-9f1c528dad2a
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_iata
    - node.type.destination_info
id: node.destination_info.field_iata
field_name: field_iata
entity_type: node
bundle: destination_info
label: 'IATA (AMA)'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
